<?php
#region region DOCS
/** @var Partido[] $torneos */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Revisar Torneos</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>

    <!-- Toast notification styles (same as epartido_probabilidades.view.php) -->
    <style>
        .floating-badge-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            z-index: 9999;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 350px;
            min-width: 200px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .floating-badge-toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .floating-badge-toast.hide {
            opacity: 0;
            transform: translateX(100%);
        }

        .floating-badge-toast.error {
            background-color: #dc3545;
        }
    </style>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <h1 class="page-header">Revisar Estado Torneos</h1>

        <hr>
        <!-- END page-header -->

        <?php #region region Main Form Structure ?>
        <form action="revisar-torneos" method="POST" id="mainTorneosForm">
            
            <!-- Button to go back to partidos -->
            <div class="row mt-3 mb-3">
                <div class="col-md-12">
                    <a href="lpartidos" class="btn btn-outline-secondary w-100">
                        <i class="fa fa-arrow-left me-1"></i> Volver a Partidos
                    </a>
                </div>
            </div>

            <?php #region region LOADING PANEL ?>
            <div id="loadingPanel" class="panel panel-inverse mt-3">
                <div class="panel-heading">
                    <h4 class="panel-title d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-spinner fa-spin me-2"></i>Cargando Torneos...</span>
                        <span class="badge bg-info fs-15px">Procesando</span>
                    </h4>
                </div>
                <div class="panel-body">
                    <!-- Statistics Section -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card bg-dark border-secondary">
                                <div class="card-body text-center py-2">
                                    <h6 class="card-title mb-1 text-muted">Partidos Pendientes</h6>
                                    <h4 class="text-warning mb-0"><?php echo $total_pending_matches; ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-dark border-secondary">
                                <div class="card-body text-center py-2">
                                    <h6 class="card-title mb-1 text-muted">Equipos Únicos</h6>
                                    <h4 class="text-info mb-0"><?php echo $total_unique_teams; ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Progress Section -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span id="progressText" class="text-muted">Obteniendo torneos de equipos...</span>
                            <span id="progressPercentage" class="text-info">0%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div id="progressBar" class="progress-bar bg-info" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>

                    <!-- Status Messages -->
                    <div id="statusMessages" class="text-center">
                        <small class="text-muted">Iniciando procesamiento de equipos...</small>
                    </div>
                </div>
            </div>
            <?php #endregion loading panel ?>

            <?php #region region PANEL torneos ?>
            <div id="torneosPanel" class="panel panel-inverse mt-3" style="display: none;">
                <div class="panel-heading">
                    <h4 class="panel-title d-flex justify-content-between align-items-center">
                        <span>Torneos Pendientes de Revisión:</span>
                        <span id="torneosCount" class="badge bg-warning fs-15px"><?php echo count($torneos); ?></span>
                    </h4>
                </div>
                <!-- BEGIN panel-body -->
                <div class="p-1 table-nowrap" style="overflow: auto">
                    <?php #region region TABLE torneos ?>
                    <table id="torneosTable" class="table table-sm table-hover">
                        <thead>
                        <tr>
                            <th class="text-center">Acciones</th>
                            <th class="text-center">Nombre del Torneo</th>
                            <th class="text-center">Temporada</th>
                            <th class="text-center">ID Footy</th>
                            <th class="text-center">Fecha Actualizado</th>
                            <th class="text-center">Días desde Actualización</th>
                        </tr>
                        </thead>
                        <tbody id="torneosTableBody" class="fs-13px">
                        <!-- Tournaments will be populated by JavaScript -->
                        </tbody>
                    </table>
                    <?php #endregion table torneos ?>
                </div>
                <!-- END panel-body -->
            </div>
            <?php #endregion panel torneos ?>

        </form>
        <?php #endregion Main Form Structure ?>

    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<script>
$(document).ready(function() {
    // Data from PHP
    const teamIds = <?php echo $team_ids_json; ?>;
    const initialTournaments = <?php echo json_encode($torneos); ?>;
    const config = <?php echo json_encode($config); ?>;

    // Progress tracking variables
    let processedTeams = 0;
    let totalTeams = teamIds.length;
    let allTeamTournaments = [];
    let errorCount = 0;

    // Start processing if there are teams to process
    if (totalTeams > 0) {
        processTeamsSequentially();
    } else {
        // No teams to process, show results immediately
        showFinalResults();
    }

    function processTeamsSequentially() {
        if (processedTeams < totalTeams) {
            const teamId = teamIds[processedTeams];

            // Update progress text
            $('#progressText').text(`Procesando equipo ${processedTeams + 1} de ${totalTeams}...`);

            // Make AJAX call for this team
            $.ajax({
                url: 'revisar-torneos',
                method: 'POST',
                dataType: 'json',
                data: {
                    ajax_process_team_tournament: 1,
                    team_id: teamId
                },
                success: function(response) {
                    if (response.status === 'success') {
                        // Add tournaments from this team to our collection
                        allTeamTournaments = allTeamTournaments.concat(response.tournaments);
                    } else {
                        console.error(`Error processing team ${teamId}:`, response.message);
                        errorCount++;
                    }
                },
                error: function(xhr, status, error) {
                    console.error(`AJAX error for team ${teamId}:`, error);
                    errorCount++;
                },
                complete: function() {
                    // Update progress
                    processedTeams++;
                    const percentage = Math.round((processedTeams / totalTeams) * 100);

                    $('#progressBar').css('width', percentage + '%').attr('aria-valuenow', percentage);
                    $('#progressPercentage').text(percentage + '%');

                    // Continue with next team or finish
                    if (processedTeams < totalTeams) {
                        // Small delay to prevent overwhelming the server
                        setTimeout(processTeamsSequentially, 100);
                    } else {
                        // All teams processed
                        finishProcessing();
                    }
                }
            });
        }
    }

    function finishProcessing() {
        // Update status
        $('#progressText').text('Procesamiento completado');
        $('#statusMessages').html('<small class="text-success">Carga completada</small>');

        // Show error summary if any
        if (errorCount > 0) {
            $('#statusMessages').append(`<br><small class="text-warning">Advertencia: ${errorCount} equipos fallaron</small>`);
        }

        // Wait a moment then show results
        setTimeout(showFinalResults, 1000);
    }

    function showFinalResults() {
        // Merge initial tournaments with team tournaments
        const mergedTournaments = mergeAndDeduplicateTournaments(initialTournaments, allTeamTournaments);

        // Sort tournaments alphabetically by tournament name (pais), then by season
        mergedTournaments.sort((a, b) => {
            // Primary sort: Tournament name (pais)
            const nameComparison = a.pais.localeCompare(b.pais);
            if (nameComparison !== 0) {
                return nameComparison;
            }

            // Secondary sort: Season
            return a.season.localeCompare(b.season);
        });

        // Populate the table
        populateTournamentsTable(mergedTournaments);

        // Update count badge
        $('#torneosCount').text(mergedTournaments.length);

        // Hide loading panel and show results
        $('#loadingPanel').fadeOut(500, function() {
            $('#torneosPanel').fadeIn(500);
        });
    }

    function mergeAndDeduplicateTournaments(initialTournaments, teamTournaments) {
        const merged = {};

        // Add initial tournaments - USE RAW ID FOR DEDUPLICATION
        initialTournaments.forEach(function(torneo) {
            const uniqueKey = torneo.raw_pais_id + '_' + torneo.season;
            merged[uniqueKey] = torneo;
        });

        // Add team tournaments (convert from API format to tournament format)
        // Only include team tournaments that have a Footy ID
        teamTournaments.forEach(function(teamTorneo) {
            // USE RAW ID FOR DEDUPLICATION
            const uniqueKey = teamTorneo.raw_pais_id + '_' + teamTorneo.season;
            if (!merged[uniqueKey] && teamTorneo.footy_id && teamTorneo.footy_id !== '') {
                // Convert team tournament to tournament object format
                merged[uniqueKey] = {
                    pais: teamTorneo.pais,
                    pais_torneo: {
                        id: teamTorneo.pais_id
                    },
                    raw_pais_id: teamTorneo.raw_pais_id, // Include raw ID
                    season: teamTorneo.season,
                    fecha_upload: teamTorneo.fecha_upload,
                    footy_id: teamTorneo.footy_id, // Use Footy ID from AJAX response
                    text_color_class: '',
                    days_diff: null
                };
            }
        });

        // Convert back to array and enrich
        const result = Object.values(merged);

        // Enrich tournaments with date calculations and colors
        result.forEach(function(torneo) {
            enrichTournament(torneo);
        });

        return result;
    }

    function enrichTournament(torneo) {
        // Calculate date-related properties
        if (torneo.fecha_upload) {
            // Parse upload date as local date to avoid timezone conversion issues
            // Create dates at midnight to compare only date parts, not time
            const dateParts = torneo.fecha_upload.split('-');
            let uploadDate, currentDate;

            if (dateParts.length === 3) {
                const year = parseInt(dateParts[0]);
                const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
                const day = parseInt(dateParts[2]);
                uploadDate = new Date(year, month, day);
            } else {
                // Fallback for other date formats
                uploadDate = new Date(torneo.fecha_upload);
            }

            // Create current date at midnight for proper comparison
            const now = new Date();
            currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            // Calculate difference in days using date-only comparison
            const diffTime = currentDate.getTime() - uploadDate.getTime();
            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

            torneo.days_diff = diffDays;

            // Apply color logic: green for 0-6 days, gray for 7+ days
            if (diffDays >= 0 && diffDays <= 6) {
                torneo.text_color_class = 'text-success';
            } else {
                torneo.text_color_class = 'text-muted';
            }
        }
    }

    function populateTournamentsTable(tournaments) {
        const tbody = $('#torneosTableBody');
        tbody.empty();

        tournaments.forEach(function(torneo) {
            const row = createTournamentRow(torneo);
            tbody.append(row);
        });
    }

    function createTournamentRow(torneo) {
        // Check if this is a priority season
        const isPriority = config && (torneo.season === config.bet_season_1 || torneo.season === config.bet_season_2);
        const priorityBadge = isPriority ? '<span class="badge bg-warning ms-2 align-middle" title="Temporada prioritaria"><i class="fas fa-star"></i></span>' : '';

        // Format date (only show date, no time)
        let formattedDate = '-';
        if (torneo.fecha_upload) {
            // Parse date as local date to avoid timezone conversion issues
            const dateParts = torneo.fecha_upload.split('-');
            if (dateParts.length === 3) {
                const year = parseInt(dateParts[0]);
                const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
                const day = parseInt(dateParts[2]);
                const date = new Date(year, month, day);
                formattedDate = date.toLocaleDateString('en-CA'); // yyyy-MM-dd format
            } else {
                // Fallback for other date formats
                formattedDate = torneo.fecha_upload.split(' ')[0]; // Take only date part if datetime
            }
        }

        // Format days diff
        let daysDiff = '-';
        if (torneo.days_diff !== null && torneo.days_diff !== undefined) {
            daysDiff = torneo.days_diff >= 0 ? torneo.days_diff : '-';
        }

        // Format Footy ID
        const footyId = torneo.footy_id ? `<span class="badge bg-info fs-12px">${torneo.footy_id}</span>` : '<span class="text-muted">-</span>';

        // Create action buttons
        let actionButtons = '<span class="text-muted">-</span>';
        if (torneo.footy_id) {
            const paisId = torneo.pais_torneo ? torneo.pais_torneo.id : '';
            const paisNombre = torneo.pais ? torneo.pais.replace(/'/g, "\\'") : '';
            actionButtons = `
                <button type="button" class="btn btn-xs btn-info"
                    onclick="actualizarViaAPI('${paisId}', '${torneo.season}', '${torneo.footy_id}', '${paisNombre}')"
                    title="Actualizar datos via API">
                    <i class="fas fa-sync me-1"></i>Actualizar via API
                </button>
            `;
        }

        // Create unique identifier for this tournament row - USE RAW ID
        const rawPaisId = torneo.raw_pais_id || '';
        const tournamentKey = `${rawPaisId}_${torneo.season}`;

        return `
            <tr data-tournament-key="${tournamentKey}">
                <td class="text-center align-middle">
                    ${actionButtons}
                </td>
                <td class="align-middle">
                    ${torneo.pais}${priorityBadge}
                </td>
                <td class="text-center align-middle">
                    ${torneo.season}
                </td>
                <td class="text-center align-middle">
                    ${footyId}
                </td>
                <td class="text-center align-middle">
                    <span class="${torneo.text_color_class}">${formattedDate}</span>
                </td>
                <td class="text-center align-middle">
                    <span class="${torneo.text_color_class}">${daysDiff}</span>
                </td>
            </tr>
        `;
    }
});

// Actualizar via API function (adapted from epartido_probabilidades.view.php)
function actualizarViaAPI(paisId, season, footyId, paisNombre) {
    // Show loading indicator
    const loadingHtml = '<div class="d-flex justify-content-center align-items-center" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); z-index: 9999;"><div class="text-center text-white"><div class="spinner-border mb-3" role="status"></div><h5>Actualizando datos via API...</h5><p>Por favor espere, esto puede tomar varios minutos.</p></div></div>';
    $('body').append(loadingHtml);

    // Disable all buttons to prevent double-clicks
    $('button').prop('disabled', true);

    // Make AJAX call to process API data
    $.ajax({
        url: 'revisar-torneos',
        method: 'POST',
        dataType: 'json',
        data: {
            sub_actualizar_via_api: 1,
            pais_id: paisId,
            season: season,
            footy_id: footyId
        },
        success: function(response) {
            // Remove loading indicator
            $('.d-flex.justify-content-center').remove();
            $('button').prop('disabled', false);

            if (response.status === 'success') {
                // Show success message with toast notification
                showToastMessage('Registros del torneo han sido actualizados.');

                // Update the specific tournament row dynamically
                updateTournamentRow(response.raw_pais_id, response.season, response.fecha_upload);
            } else {
                // Show error message with debug info
                let errorMessage = response.message || 'Error al procesar los datos de la API';
                if (response.debug_info) {
                    errorMessage += '\n\nDebug Info:\n';
                    if (response.debug_info.pais_id_raw !== undefined) {
                        errorMessage += 'País ID (raw): ' + response.debug_info.pais_id_raw + '\n';
                    }
                    errorMessage += 'País ID (decodificado): ' + response.debug_info.pais_id + '\n';
                    errorMessage += 'Season: ' + response.debug_info.season + '\n';
                    errorMessage += 'Footy ID: ' + response.debug_info.footy_id + '\n';
                    errorMessage += 'Error Line: ' + response.debug_info.error_line + '\n';
                    errorMessage += 'Error File: ' + response.debug_info.error_file;
                }

                swal({
                    title: 'Error',
                    text: errorMessage,
                    icon: 'error',
                    buttons: {
                        confirm: {
                            text: 'Ok',
                            value: true,
                            visible: true,
                            className: 'btn btn-danger',
                            closeModal: true
                        }
                    }
                });
            }
        },
        error: function(xhr, status, error) {
            // Remove loading indicator
            $('.d-flex.justify-content-center').remove();
            $('button').prop('disabled', false);

            console.error('AJAX Error:', error);
            console.error('Status:', status);
            console.error('Response Text:', xhr.responseText);
            console.error('Response Status:', xhr.status);
            console.error('Response Headers:', xhr.getAllResponseHeaders());

            let errorText = 'Error al procesar la solicitud. Por favor, intente nuevamente.';
            if (xhr.responseText) {
                errorText += '\n\nResponse: ' + xhr.responseText;
            }

            swal({
                title: 'Error AJAX',
                text: errorText,
                icon: 'error',
                buttons: {
                    confirm: {
                        text: 'Ok',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                }
            });
        },
        timeout: 300000 // 5 minutes timeout
    });
}

// Function to update a specific tournament row after API update
function updateTournamentRow(paisId, season, newFechaUpload) {
    const tournamentKey = `${paisId}_${season}`;
    const row = $(`tr[data-tournament-key="${tournamentKey}"]`);

    if (row.length > 0) {
        // Format the new date (yyyy-MM-dd format only)
        let formattedDate = '-';
        if (newFechaUpload) {
            // Parse date as local date to avoid timezone conversion issues
            // If newFechaUpload is "2025-11-04", we want to display "2025-11-04"
            const dateParts = newFechaUpload.split('-');
            if (dateParts.length === 3) {
                const year = parseInt(dateParts[0]);
                const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
                const day = parseInt(dateParts[2]);
                const date = new Date(year, month, day);
                formattedDate = date.toLocaleDateString('en-CA'); // yyyy-MM-dd format
            } else {
                // Fallback for other date formats
                formattedDate = newFechaUpload;
            }
        }

        // Calculate days difference
        let daysDiff = '-';
        let textColorClass = 'text-muted';

        if (newFechaUpload) {
            // Parse upload date as local date to avoid timezone conversion issues
            // Create dates at midnight to compare only date parts, not time
            const dateParts = newFechaUpload.split('-');
            let uploadDate, currentDate;

            if (dateParts.length === 3) {
                const year = parseInt(dateParts[0]);
                const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
                const day = parseInt(dateParts[2]);
                uploadDate = new Date(year, month, day);
            } else {
                uploadDate = new Date(newFechaUpload);
            }

            // Create current date at midnight for proper comparison
            const now = new Date();
            currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            // Calculate difference in days using date-only comparison
            const diffTime = currentDate.getTime() - uploadDate.getTime();
            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

            daysDiff = diffDays >= 0 ? diffDays : '-';

            // Apply color logic: green for 0-6 days, gray for 7+ days
            if (diffDays >= 0 && diffDays <= 6) {
                textColorClass = 'text-success';
            } else {
                textColorClass = 'text-muted';
            }
        }

        // Update the "Fecha Actualizado" column (5th column, index 4)
        row.find('td:eq(4) span').removeClass('text-success text-muted').addClass(textColorClass).text(formattedDate);

        // Update the "Días desde Actualización" column (6th column, index 5)
        row.find('td:eq(5) span').removeClass('text-success text-muted').addClass(textColorClass).text(daysDiff);
    }
}

// Toast notification function (same as epartido_probabilidades.view.php)
function showToastMessage(message) {
    const toast = document.createElement('div');

    // Handle multi-line messages by converting \n to <br>
    const formattedMessage = message.replace(/\n/g, '<br>');
    toast.innerHTML = formattedMessage;

    toast.classList.add('floating-badge-toast');
    document.body.appendChild(toast);
    setTimeout(() => { toast.classList.add('show'); }, 10);
    setTimeout(() => {
        toast.classList.add('hide');
        setTimeout(() => { toast.remove(); }, 300);
    }, 3000); // Extended to 3 seconds for API success messages
}
</script>

<?php #endregion js ?>

</body>
</html>

